{"name": "discord-store-bot", "version": "1.0.0", "description": "Bot Discord modular para loja com integração MongoDB", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "deploy-commands": "node scripts/deploy-commands.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["discord", "bot", "store", "mongodb", "modular"], "author": "", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "dotenv": "^16.3.1", "mercadopago": "^2.8.0", "mongoose": "^8.0.3", "node-cache": "^5.1.2"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}, "jest": {"testEnvironment": "node", "testMatch": ["**/tests/**/*.test.js"], "collectCoverageFrom": ["src/**/*.js", "index.js", "!src/**/*.test.js"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"]}, "engines": {"node": ">=16.11.0"}}