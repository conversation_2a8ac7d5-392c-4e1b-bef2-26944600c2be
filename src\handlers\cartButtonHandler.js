import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } from 'discord.js';
import { logger } from '../utils/logger.js';
import ShoppingCart from '../models/ShoppingCart.js';
import { fixedCartEmbedHandler } from './fixedCartEmbedHandler.js';
import { pixPaymentManager } from '../utils/pixPaymentManager.js';
import { COLORS, EMOJIS, BOT_CONFIG } from '../config/constants.js';

/**
 * Handler para botões do carrinho de compras
 */
export class CartButtonHandler {
    /**
     * Processa interações de botões do carrinho
     * @param {Object} interaction - Interação do Discord
     */
    static async handleCartButton(interaction) {
        const customId = interaction.customId;

        try {
            switch (customId) {
                case 'cart_clear':
                    await this.handleClearCart(interaction);
                    break;
                case 'cart_checkout':
                    await this.handleCheckout(interaction);
                    break;
                case 'cart_cancel_payment':
                    await this.handleCancelPayment(interaction);
                    break;
                case 'cart_manage_items':
                    await this.handleManageItems(interaction);
                    break;
                default:
                    logger.warn(`Botão de carrinho não reconhecido: ${customId}`);
                    await interaction.reply({
                        content: '❌ Ação não reconhecida.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            logger.error('Erro ao processar botão do carrinho:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar sua solicitação.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Limpa o carrinho de compras
     * @param {Object} interaction - Interação do Discord
     */
    static async handleClearCart(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id, 
                interaction.guild.id
            );

            if (!cart) {
                return await interaction.editReply({
                    content: '❌ Nenhum carrinho ativo encontrado.'
                });
            }

            // Limpa o carrinho
            await cart.clearCart();

            // Atualiza embed fixo para mostrar carrinho vazio
            await fixedCartEmbedHandler.createOrUpdateCartEmbed(
                interaction.channel,
                cart
            );

            await interaction.editReply({
                content: `${EMOJIS.SUCCESS} Carrinho limpo com sucesso!`
            });

            logger.info(`Carrinho limpo pelo usuário ${interaction.user.tag}`);

        } catch (error) {
            logger.error('Erro ao limpar carrinho:', error);
            await interaction.editReply({
                content: '❌ Erro ao limpar o carrinho.'
            });
        }
    }

    /**
     * Inicia processo de checkout
     * @param {Object} interaction - Interação do Discord
     */
    static async handleCheckout(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id, 
                interaction.guild.id
            );

            if (!cart || !cart.items || cart.items.length === 0) {
                return await interaction.editReply({
                    content: '❌ Seu carrinho está vazio.'
                });
            }

            // Valida valor mínimo
            if (cart.subtotal < BOT_CONFIG.STORE.MIN_ORDER_VALUE) {
                return await interaction.editReply({
                    content: `❌ Valor mínimo para pedido: ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${BOT_CONFIG.STORE.MIN_ORDER_VALUE.toFixed(2)}`
                });
            }

            // Cria pagamento PIX
            const paymentData = {
                amount: cart.subtotal,
                description: `Compra na loja ${cart.storeName}`,
                payerName: interaction.user.username,
                payerEmail: `${interaction.user.id}@discord.com`,
                externalReference: `cart_${cart._id}`
            };

            const pixPayment = await pixPaymentManager.createPixPayment(
                paymentData,
                interaction.guild.id
            );

            // Atualiza embed com informações de pagamento
            await fixedCartEmbedHandler.createOrUpdateCartEmbed(
                interaction.channel,
                cart,
                {
                    paymentInfo: pixPayment,
                    isPaymentPending: true
                }
            );

            // Inicia polling do pagamento
            await pixPaymentManager.startPaymentPolling(
                pixPayment.id,
                interaction.guild.id,
                async (status) => {
                    await this.handlePaymentStatusChange(
                        interaction.channel,
                        cart,
                        status
                    );
                },
                {
                    onTimeout: async () => {
                        await this.handlePaymentTimeout(interaction.channel, cart);
                    }
                }
            );

            await interaction.editReply({
                content: 
                    `${EMOJIS.SUCCESS} **Pagamento PIX gerado!**\n\n` +
                    `💳 **Valor:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${pixPayment.amount.toFixed(2)}\n` +
                    `⏰ **Expira em:** 15 minutos\n\n` +
                    `📱 **Como pagar:**\n` +
                    `• Abra seu app do banco\n` +
                    `• Escaneie o QR Code no embed acima\n` +
                    `• Confirme o pagamento\n\n` +
                    `🔄 O status será atualizado automaticamente quando o pagamento for confirmado.`
            });

            logger.info(`Checkout iniciado para carrinho ${cart._id} - Pagamento: ${pixPayment.id}`);

        } catch (error) {
            logger.error('Erro no checkout:', error);
            await interaction.editReply({
                content: '❌ Erro ao processar checkout. Verifique se o MercadoPago está configurado.'
            });
        }
    }

    /**
     * Cancela pagamento pendente
     * @param {Object} interaction - Interação do Discord
     */
    static async handleCancelPayment(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id, 
                interaction.guild.id
            );

            if (!cart) {
                return await interaction.editReply({
                    content: '❌ Nenhum carrinho ativo encontrado.'
                });
            }

            // Para polling se existir
            // Note: Precisaríamos armazenar o paymentId no carrinho para isso
            // Por simplicidade, vamos apenas atualizar o embed

            // Atualiza embed para estado normal
            await fixedCartEmbedHandler.createOrUpdateCartEmbed(
                interaction.channel,
                cart
            );

            await interaction.editReply({
                content: `${EMOJIS.SUCCESS} Pagamento cancelado. Você pode tentar novamente quando quiser.`
            });

            logger.info(`Pagamento cancelado pelo usuário ${interaction.user.tag}`);

        } catch (error) {
            logger.error('Erro ao cancelar pagamento:', error);
            await interaction.editReply({
                content: '❌ Erro ao cancelar pagamento.'
            });
        }
    }

    /**
     * Abre modal para gerenciar itens do carrinho
     * @param {Object} interaction - Interação do Discord
     */
    static async handleManageItems(interaction) {
        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id, 
                interaction.guild.id
            );

            if (!cart || !cart.items || cart.items.length === 0) {
                return await interaction.reply({
                    content: '❌ Seu carrinho está vazio.',
                    ephemeral: true
                });
            }

            // Cria modal para gerenciar quantidades
            const modal = new ModalBuilder()
                .setCustomId('cart_manage_modal')
                .setTitle('Gerenciar Itens do Carrinho');

            // Adiciona campos para cada item (máximo 5 por limitação do Discord)
            const itemsToShow = cart.items.slice(0, 5);
            
            itemsToShow.forEach((item, index) => {
                const input = new TextInputBuilder()
                    .setCustomId(`item_quantity_${index}`)
                    .setLabel(`${item.productName} (atual: ${item.quantity})`)
                    .setStyle(TextInputStyle.Short)
                    .setPlaceholder('Digite a nova quantidade (0 para remover)')
                    .setValue(item.quantity.toString())
                    .setRequired(true)
                    .setMaxLength(2);

                const actionRow = new ActionRowBuilder().addComponents(input);
                modal.addComponents(actionRow);
            });

            await interaction.showModal(modal);

        } catch (error) {
            logger.error('Erro ao abrir modal de gerenciamento:', error);
            await interaction.reply({
                content: '❌ Erro ao abrir gerenciamento de itens.',
                ephemeral: true
            });
        }
    }

    /**
     * Processa mudança de status do pagamento
     * @param {Object} channel - Canal do Discord
     * @param {Object} cart - Carrinho de compras
     * @param {Object} status - Status do pagamento
     */
    static async handlePaymentStatusChange(channel, cart, status) {
        try {
            if (status.approved) {
                // Pagamento aprovado
                const embed = new EmbedBuilder()
                    .setColor(COLORS.SUCCESS)
                    .setTitle(`${EMOJIS.SUCCESS} Pagamento Confirmado!`)
                    .setDescription(
                        `✅ **Seu pagamento foi confirmado com sucesso!**\n\n` +
                        `💰 **Valor:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${cart.subtotal.toFixed(2)}\n` +
                        `📦 **Itens:** ${cart.itemCount}\n\n` +
                        `🎉 **Próximos passos:**\n` +
                        `• Seus itens foram processados\n` +
                        `• Você receberá os produtos em breve\n` +
                        `• Este canal será fechado automaticamente`
                    )
                    .setTimestamp();

                await channel.send({ embeds: [embed] });

                // Marca carrinho como completo
                await cart.markAsCompleted();

                logger.info(`Pagamento confirmado para carrinho ${cart._id}`);

            } else if (status.rejected || status.cancelled) {
                // Pagamento rejeitado/cancelado
                await fixedCartEmbedHandler.createOrUpdateCartEmbed(channel, cart);

                const embed = new EmbedBuilder()
                    .setColor(COLORS.ERROR)
                    .setTitle(`${EMOJIS.ERROR} Pagamento ${status.rejected ? 'Rejeitado' : 'Cancelado'}`)
                    .setDescription(
                        `❌ **Seu pagamento não foi processado.**\n\n` +
                        `🔄 **Você pode tentar novamente:**\n` +
                        `• Clique em "Finalizar Compra" novamente\n` +
                        `• Verifique os dados do pagamento\n` +
                        `• Entre em contato se o problema persistir`
                    )
                    .setTimestamp();

                await channel.send({ embeds: [embed] });

                logger.info(`Pagamento ${status.status} para carrinho ${cart._id}`);
            }

        } catch (error) {
            logger.error('Erro ao processar mudança de status:', error);
        }
    }

    /**
     * Processa timeout do pagamento
     * @param {Object} channel - Canal do Discord
     * @param {Object} cart - Carrinho de compras
     */
    static async handlePaymentTimeout(channel, cart) {
        try {
            // Volta embed para estado normal
            await fixedCartEmbedHandler.createOrUpdateCartEmbed(channel, cart);

            const embed = new EmbedBuilder()
                .setColor(COLORS.WARNING)
                .setTitle(`${EMOJIS.WARNING} Pagamento Expirado`)
                .setDescription(
                    `⏰ **O tempo para pagamento expirou.**\n\n` +
                    `🔄 **Para continuar:**\n` +
                    `• Clique em "Finalizar Compra" novamente\n` +
                    `• Um novo QR Code será gerado\n` +
                    `• Complete o pagamento em até 15 minutos`
                )
                .setTimestamp();

            await channel.send({ embeds: [embed] });

            logger.info(`Pagamento expirado para carrinho ${cart._id}`);

        } catch (error) {
            logger.error('Erro ao processar timeout de pagamento:', error);
        }
    }
}

export default CartButtonHandler;
