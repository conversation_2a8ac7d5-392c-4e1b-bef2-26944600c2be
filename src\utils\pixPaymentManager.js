import { MercadoPagoConfig, Payment } from 'mercadopago';
import { logger } from './logger.js';
import BotConfig from '../models/BotConfig.js';

/**
 * Gerenciador de pagamentos PIX via MercadoPago
 */
export class PixPaymentManager {
    constructor() {
        this.client = null;
        this.activePollings = new Map(); // Map para controlar polling ativo por paymentId
    }

    /**
     * Inicializa o cliente MercadoPago para uma guild específica
     * @param {string} guildId - ID da guild
     * @returns {Promise<boolean>} - True se inicializado com sucesso
     */
    async initializeClient(guildId) {
        try {
            const config = await BotConfig.findByGuild(guildId);
            
            if (!config || !config.mercadoPago || !config.mercadoPago.isEnabled) {
                throw new Error('MercadoPago não configurado para esta guild');
            }

            if (!config.mercadoPago.accessToken) {
                throw new Error('Access Token do MercadoPago não configurado');
            }

            this.client = new MercadoPagoConfig({
                accessToken: config.mercadoPago.accessToken,
                options: {
                    timeout: 5000,
                    idempotencyKey: 'abc'
                }
            });

            logger.info(`Cliente MercadoPago inicializado para guild ${guildId}`);
            return true;

        } catch (error) {
            logger.error('Erro ao inicializar cliente MercadoPago:', error);
            return false;
        }
    }

    /**
     * Cria um pagamento PIX
     * @param {Object} paymentData - Dados do pagamento
     * @param {string} guildId - ID da guild
     * @returns {Promise<Object>} - Dados do pagamento criado
     */
    async createPixPayment(paymentData, guildId) {
        try {
            const initialized = await this.initializeClient(guildId);
            if (!initialized) {
                throw new Error('Falha ao inicializar cliente MercadoPago');
            }

            const payment = new Payment(this.client);

            const paymentRequest = {
                transaction_amount: paymentData.amount,
                description: paymentData.description || 'Compra na loja Discord',
                payment_method_id: 'pix',
                payer: {
                    email: paymentData.payerEmail || '<EMAIL>',
                    first_name: paymentData.payerName || 'Cliente',
                    last_name: 'Discord',
                    identification: {
                        type: 'CPF',
                        number: '12345678901' // CPF genérico para testes
                    }
                },
                notification_url: paymentData.webhookUrl,
                external_reference: paymentData.externalReference,
                date_of_expiration: new Date(Date.now() + 15 * 60 * 1000).toISOString() // 15 minutos
            };

            const result = await payment.create({ body: paymentRequest });
            
            logger.info(`Pagamento PIX criado: ${result.id}`);
            
            return {
                id: result.id,
                status: result.status,
                qrCode: result.point_of_interaction?.transaction_data?.qr_code,
                qrCodeBase64: result.point_of_interaction?.transaction_data?.qr_code_base64,
                ticketUrl: result.point_of_interaction?.transaction_data?.ticket_url,
                expirationDate: result.date_of_expiration,
                amount: result.transaction_amount
            };

        } catch (error) {
            logger.error('Erro ao criar pagamento PIX:', error);
            throw error;
        }
    }

    /**
     * Verifica o status de um pagamento
     * @param {string} paymentId - ID do pagamento
     * @param {string} guildId - ID da guild
     * @returns {Promise<Object>} - Status do pagamento
     */
    async checkPaymentStatus(paymentId, guildId) {
        try {
            const initialized = await this.initializeClient(guildId);
            if (!initialized) {
                throw new Error('Falha ao inicializar cliente MercadoPago');
            }

            const payment = new Payment(this.client);
            const result = await payment.get({ id: paymentId });

            return {
                id: result.id,
                status: result.status,
                statusDetail: result.status_detail,
                approved: result.status === 'approved',
                rejected: result.status === 'rejected',
                cancelled: result.status === 'cancelled',
                pending: result.status === 'pending'
            };

        } catch (error) {
            logger.error(`Erro ao verificar status do pagamento ${paymentId}:`, error);
            throw error;
        }
    }

    /**
     * Inicia polling automático para verificar status do pagamento
     * @param {string} paymentId - ID do pagamento
     * @param {string} guildId - ID da guild
     * @param {Function} onStatusChange - Callback chamado quando status muda
     * @param {Object} options - Opções do polling
     * @returns {Promise<void>}
     */
    async startPaymentPolling(paymentId, guildId, onStatusChange, options = {}) {
        const {
            interval = 10000, // 10 segundos
            maxAttempts = 90,  // 15 minutos total (90 * 10s)
            onTimeout = null
        } = options;

        // Evita polling duplicado
        if (this.activePollings.has(paymentId)) {
            logger.warn(`Polling já ativo para pagamento ${paymentId}`);
            return;
        }

        let attempts = 0;
        let lastStatus = null;

        const pollInterval = setInterval(async () => {
            try {
                attempts++;
                
                const status = await this.checkPaymentStatus(paymentId, guildId);
                
                // Chama callback se status mudou
                if (status.status !== lastStatus) {
                    lastStatus = status.status;
                    await onStatusChange(status);
                }

                // Para polling se pagamento foi finalizado
                if (status.approved || status.rejected || status.cancelled) {
                    this.stopPaymentPolling(paymentId);
                    return;
                }

                // Para polling se atingiu máximo de tentativas
                if (attempts >= maxAttempts) {
                    this.stopPaymentPolling(paymentId);
                    if (onTimeout) {
                        await onTimeout();
                    }
                    return;
                }

            } catch (error) {
                logger.error(`Erro no polling do pagamento ${paymentId}:`, error);
                
                // Para polling em caso de erro crítico
                if (attempts >= 5) {
                    this.stopPaymentPolling(paymentId);
                }
            }
        }, interval);

        // Armazena referência do polling
        this.activePollings.set(paymentId, {
            interval: pollInterval,
            startTime: Date.now(),
            attempts: 0
        });

        logger.info(`Polling iniciado para pagamento ${paymentId}`);
    }

    /**
     * Para o polling de um pagamento específico
     * @param {string} paymentId - ID do pagamento
     */
    stopPaymentPolling(paymentId) {
        const polling = this.activePollings.get(paymentId);
        if (polling) {
            clearInterval(polling.interval);
            this.activePollings.delete(paymentId);
            logger.info(`Polling parado para pagamento ${paymentId}`);
        }
    }

    /**
     * Para todos os pollings ativos
     */
    stopAllPollings() {
        for (const [paymentId, polling] of this.activePollings) {
            clearInterval(polling.interval);
            logger.info(`Polling parado para pagamento ${paymentId}`);
        }
        this.activePollings.clear();
    }

    /**
     * Obtém informações sobre pollings ativos
     * @returns {Array} - Lista de pollings ativos
     */
    getActivePollings() {
        const pollings = [];
        for (const [paymentId, polling] of this.activePollings) {
            pollings.push({
                paymentId,
                startTime: polling.startTime,
                duration: Date.now() - polling.startTime,
                attempts: polling.attempts
            });
        }
        return pollings;
    }
}

// Instância singleton
export const pixPaymentManager = new PixPaymentManager();
